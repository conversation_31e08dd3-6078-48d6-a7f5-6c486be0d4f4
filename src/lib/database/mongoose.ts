import mongoose from 'mongoose';
import { env } from '@/src/lib/config/environment';

if (!env.MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env');
}

declare global {
  var mongoose: {
    conn: mongoose.Connection | null;
    promise: Promise<mongoose.Connection> | null;
  } | undefined;
}

let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectToDatabase(): Promise<mongoose.Connection> {
  if (cached!.conn) {
    return cached!.conn;
  }

  if (!cached!.promise) {
    const opts = {
      bufferCommands: false,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 30000, // Increased from 5000ms to 30000ms
      socketTimeoutMS: 45000,
      connectTimeoutMS: 30000, // Added explicit connect timeout
      family: 4, // Use IPv4, skip trying IPv6
      retryWrites: true,
      w: 'majority',
      // Add additional connection options for better reliability
      maxIdleTimeMS: 30000,
      heartbeatFrequencyMS: 10000,
    };

    cached!.promise = mongoose.connect(env.MONGODB_URI, opts).then((mongoose) => {
      console.log('✅ Connected to MongoDB via Mongoose');

      // Set up connection event handlers
      mongoose.connection.on('error', (error) => {
        console.error('❌ MongoDB connection error:', error);
      });

      mongoose.connection.on('disconnected', () => {
        console.warn('⚠️ MongoDB disconnected');
        cached!.conn = null;
        cached!.promise = null;
      });

      mongoose.connection.on('reconnected', () => {
        console.log('🔄 MongoDB reconnected');
      });

      return mongoose.connection;
    }).catch((error) => {
      console.error('❌ MongoDB connection error:', error);
      cached!.promise = null;
      throw error;
    });
  }

  try {
    cached!.conn = await cached!.promise;
  } catch (e) {
    cached!.promise = null;
    throw e;
  }

  return cached!.conn;
}

export default connectToDatabase;
